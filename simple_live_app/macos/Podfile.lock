PODS:
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - media_kit_libs_macos_video (1.0.4):
    - FlutterMacOS
  - media_kit_native_event_loop (1.0.0):
    - FlutterMacOS
  - media_kit_video (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - screen_brightness_macos (0.1.0):
    - FlutterMacOS
  - screen_retriever (0.0.1):
    - FlutterMacOS
  - share_plus (0.0.1):
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS
  - window_manager (0.2.0):
    - FlutterMacOS

DEPENDENCIES:
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - media_kit_libs_macos_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos`)
  - media_kit_native_event_loop (from `Flutter/ephemeral/.symlinks/plugins/media_kit_native_event_loop/macos`)
  - media_kit_video (from `Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - screen_brightness_macos (from `Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos`)
  - screen_retriever (from `Flutter/ephemeral/.symlinks/plugins/screen_retriever/macos`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)
  - window_manager (from `Flutter/ephemeral/.symlinks/plugins/window_manager/macos`)

EXTERNAL SOURCES:
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  media_kit_libs_macos_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_libs_macos_video/macos
  media_kit_native_event_loop:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_native_event_loop/macos
  media_kit_video:
    :path: Flutter/ephemeral/.symlinks/plugins/media_kit_video/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  screen_brightness_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_brightness_macos/macos
  screen_retriever:
    :path: Flutter/ephemeral/.symlinks/plugins/screen_retriever/macos
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos
  window_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/window_manager/macos

SPEC CHECKSUMS:
  device_info_plus: 5401765fde0b8d062a2f8eb65510fb17e77cf07f
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  media_kit_libs_macos_video: b3e2bbec2eef97c285f2b1baa7963c67c753fb82
  media_kit_native_event_loop: 81fd5b45192b72f8b5b69eaf5b540f45777eb8d5
  media_kit_video: c75b07f14d59706c775778e4dd47dd027de8d1e5
  package_info_plus: 02d7a575e80f194102bef286361c6c326e4c29ce
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  screen_brightness_macos: 2d6d3af2165592d9a55ffcd95b7550970e41ebda
  screen_retriever: 59634572a57080243dd1bf715e55b6c54f241a38
  share_plus: 76dd39142738f7a68dd57b05093b5e8193f220f7
  url_launcher_macos: d2691c7dd33ed713bf3544850a623080ec693d95
  wakelock_plus: 4783562c9a43d209c458cb9b30692134af456269
  window_manager: 3a1844359a6295ab1e47659b1a777e36773cd6e8

PODFILE CHECKSUM: 236401fc2c932af29a9fcf0e97baeeb2d750d367

COCOAPODS: 1.11.3
