name: simple_live_tv_app
description: A new Flutter project.
publish_to: 'none'
version: 1.3.9+10309

environment:
  sdk: '>=3.1.2 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  simple_live_core:
    path: ../simple_live_core
  cupertino_icons: ^1.0.2
  remixicon: ^1.0.0 #Remix图标

  # 框架、工具
  get: ^4.6.6 #状态管理、路由管理、国际化
  dio: ^5.3.2 #网络请求
  hive: 2.2.3 #持久化存储
  hive_flutter: 1.1.0 #持久化存储
  logger: ^2.0.2 #日志
  intl: ^0.18.1 #国际化
  flutter_screenutil: ^5.9.0 #UI适配
  uuid: ^4.3.3 #UUID
  signalr_netcore: ^1.3.9 #SignalR

  #Widget
  flutter_staggered_grid_view: ^0.7.0 #瀑布流/GridView
  flutter_easyrefresh: 2.2.2 #下拉刷新、上拉加载
  extended_image: ^9.1.0 #拓展Image,支持缓存
  flutter_smart_dialog: 4.9.6 #各种弹窗 Toast/Dialog/Popup
  marquee: ^2.2.3 #跑马灯
  lottie: ^3.1.0 #Lottie动画
  qr_flutter: ^4.1.0 #二维码
  sticky_headers: ^0.3.0+2 #吸顶
  ns_danmaku: #弹幕
    git: 
        url: https://github.com/xiaoyaocz/flutter_ns_danmaku.git
        tag: v0.0.9

  #系统交互
  package_info_plus: ^8.0.0 #包信息
  url_launcher: ^6.2.5 #打开链接
  network_info_plus: ^4.1.0+1
  shelf: ^1.4.1 
  shelf_router: ^1.1.4 
  udp: ^5.0.3 #UDP
  device_info_plus: ^9.1.2 #设备信息
  wakelock_plus: ^1.2.11 #屏幕常亮，media_kit中自带，但似乎不生效
  
  # 视频播放
  media_kit: 1.1.11
  media_kit_video: 1.2.5
  media_kit_libs_video: 1.0.5

dependency_overrides: 
  fading_edge_scrollview: ^4.1.1
  screen_brightness: ^2.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true
  assets: 
    - assets/statement.txt
    - assets/images/
    - assets/icons/
    - assets/lotties/