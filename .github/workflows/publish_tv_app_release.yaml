name: app-build-action
#推送Tag时触发
on:
  push:
    tags:
      - "tv_*"
jobs:
  build-tv:
    runs-on: macos-latest
    permissions:
      contents: write
    steps:
      #签出代码 
      - uses: actions/checkout@v4
        with:
          ref: master
        
      #APK签名设置
      - name: Download Android keystore
        id: android_tv_keystore
        uses: timheuer/base64-to-file@v1.2
        with:
          fileName: keystore.jks
          encodedString: ${{ secrets.TV_KEYSTORE_BASE64 }}
      - name: Create key.properties
        run: |
          echo "storeFile=${{ steps.android_tv_keystore.outputs.filePath }}" > simple_live_tv_app/android/key.properties
          echo "storePassword=${{ secrets.TV_STORE_PASSWORD }}" >> simple_live_tv_app/android/key.properties
          echo "keyPassword=${{ secrets.TV_KEY_PASSWORD }}" >> simple_live_tv_app/android/key.properties
          echo "keyAlias=${{ secrets.TV_KEY_ALIAS }}" >> simple_live_tv_app/android/key.properties

      # 设置JAVA环境
      - uses: actions/setup-java@v4
        with:
          distribution: 'zulu'
          java-version: "17"
          cache: "gradle"
    
      #设置Flutter
      - name: Flutter action
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.22.x'
          cache: true

      #更新Flutter的packages
      - name: Restore packages
        run: |
          cd simple_live_tv_app
          flutter pub get
        
      #打包APK
      - name: Build APK
        run: |
          cd simple_live_tv_app
          flutter build apk --release --split-per-abi
    
      #上传APK至Artifacts
      - name: Upload APK to Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: android_tv
          path: |
            simple_live_tv_app/build/app/outputs/flutter-apk/app-armeabi-v7a-release.apk
            simple_live_tv_app/build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
            simple_live_tv_app/build/app/outputs/flutter-apk/app-x86_64-release.apk
    
      #读取版本信息
      - name: Read version
        id: version
        uses: juliangruber/read-file-action@v1
        with:
          path: assets/tv_app_version.json
      - name: Echo version
        run: echo "${{ fromJson(steps.version.outputs.content).version }}"
      - name: Echo version content
        run: echo "${{ fromJson(steps.version.outputs.content).version_desc }}"
    
      #上传至Release
      - name: Upload Release
        uses: softprops/action-gh-release@v1
        with:
          name: "${{ fromJson(steps.version.outputs.content).version }}"
          body: "# Android TV \n${{ fromJson(steps.version.outputs.content).version_desc }}"
          prerelease: ${{ fromJson(steps.version.outputs.content).prerelease }}
          token: ${{ secrets.TOKEN }}
          files: |
            simple_live_tv_app/build/app/outputs/flutter-apk/app-x86_64-release.apk
            simple_live_tv_app/build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
            simple_live_tv_app/build/app/outputs/flutter-apk/app-armeabi-v7a-release.apk
        
      #完成
      - run: echo "🍏 This job's status is ${{ job.status }}."