/// 路由路径
class RoutePath {
  /// 首页
  static const kIndex = "/index";

  /// 搜索
  static const kSearch = "/search";

  /// 分类详情
  static const kCategoryDetail = "/category/detail";

  /// 直播间
  static const kLiveRoomDetail = "/room/detail";

  /// 弹幕设置
  static const kSettingsDanmu = "/settings/danmu";

  /// 定时关闭设置
  static const kSettingsAutoExit = "/settings/auto_exit";

  /// 直播间设置
  static const kSettingsPlay = "/settings/play";

  /// 弹幕关键词屏蔽
  static const kSettingsDanmuShield = "/settings/danmu/shield";

  /// 其他设置
  static const kSettingsOther = "/settings/other";

  /// 赞助
  static const kSponsor = "/sponsor";

  /// 历史记录
  static const kHistory = "/user/history";

  /// 我的关注
  static const kFollowUser = "/user/follow";

  /// 工具箱
  static const kTools = "/other/tools";

  /// 主页设置
  static const kSettingsIndexed = "/settings/indexed";

  /// 外观设置
  static const kAppstyleSetting = "/settings/appstyle";

  /// 账号管理
  static const kSettingsAccount = "/settings/account";

  /// 关注设置
  static const kSettingsFollow = "/settings/follow";

  /// BiliBili Web登录
  static const kBiliBiliWebLogin = "/settings/account/bilibili/web_login";

  /// BiliBili 二维码登录
  static const kBiliBiliQRLogin = "/settings/account/bilibili/qr_login";

  /// 数据同步
  static const kLocalSync = "/local_sync";

  /// 数据同步
  static const kSync = "/sync";

  /// 扫描
  static const kSyncScan = "/sync/scan";

  /// 同步设备
  static const kLocalSyncDevice = "/sync/device";

  /// 远程同步-房间
  static const kRemoteSyncRoom = "/remote_sync/room";

  /// 远程同步-WebDAV
  static const kRemoteSyncWebDav = "/remote_sync/webDAV";

  /// 远程同步-WebDAVConfig
  static const kRemoteSyncWebDavConfig = "/remote_sync/webDAVConfig";

  /// 测试页面
  static const kTest = "/test";
}
